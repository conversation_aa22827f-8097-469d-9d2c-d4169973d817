{"name": "react-ui-lib", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@chromatic-com/storybook": "^4.1.1", "@eslint/js": "^9.33.0", "@storybook/addon-a11y": "^9.1.3", "@storybook/addon-docs": "^9.1.3", "@storybook/addon-onboarding": "^9.1.3", "@storybook/addon-vitest": "^9.1.3", "@storybook/react-vite": "^9.1.3", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-storybook": "^9.1.3", "globals": "^16.3.0", "jsdom": "^26.1.0", "playwright": "^1.55.0", "postcss": "^8.5.6", "storybook": "^9.1.3", "tailwindcss": "^4.1.12", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2", "vitest": "^3.2.4"}}